import { PriceData, StrategyConfig } from '../../../../shared/types';
export interface TradingDecision {
    shouldTrade: boolean;
    direction?: 'high' | 'low';
    confidence?: number;
    reason?: string;
}
export declare abstract class TradingStrategy {
    protected config: StrategyConfig;
    protected priceHistory: PriceData[];
    constructor(config: StrategyConfig);
    abstract getName(): string;
    abstract getDescription(): string;
    abstract evaluate(priceData: PriceData): Promise<TradingDecision>;
    updateConfig(config: StrategyConfig): void;
    addPriceData(priceData: PriceData): void;
    protected getSimpleMovingAverage(period: number): number | null;
    protected calculateRSI(period?: number): number | null;
    protected getStandardDeviation(values: number[]): number;
}
//# sourceMappingURL=TradingStrategy.d.ts.map