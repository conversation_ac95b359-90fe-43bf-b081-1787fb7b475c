import { PriceData } from '../../../../shared/types';
import { TradingStrategy, TradingDecision } from './TradingStrategy';
export declare class CustomStrategy extends TradingStrategy {
    getName(): string;
    getDescription(): string;
    evaluate(priceData: PriceData): Promise<TradingDecision>;
    validateCustomCode(code: string): {
        isValid: boolean;
        errors: string[];
    };
    getTemplateCode(): string;
}
//# sourceMappingURL=CustomStrategy.d.ts.map