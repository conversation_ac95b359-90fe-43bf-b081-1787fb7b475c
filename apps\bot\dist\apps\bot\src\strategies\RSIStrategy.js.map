{"version": 3, "file": "RSIStrategy.js", "sourceRoot": "", "sources": ["../../../../../src/strategies/RSIStrategy.ts"], "names": [], "mappings": ";;;AACA,uDAAoE;AAEpE,MAAa,WAAY,SAAQ,iCAAe;IAC/C,OAAO;QACN,OAAO,cAAc,CAAA;IACtB,CAAC;IAED,cAAc;QACb,OAAO,0EAA0E,CAAA;IAClF,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,SAAoB;QAClC,oCAAoC;QACpC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAA;QAE5B,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,EAAE,CAAA;QAC7C,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,IAAI,EAAE,CAAA;QACrD,MAAM,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,IAAI,EAAE,CAAA;QAEzD,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAA;QAExC,IAAI,GAAG,KAAK,IAAI,EAAE,CAAC;YAClB,OAAO;gBACN,WAAW,EAAE,KAAK;gBAClB,MAAM,EAAE,+CAA+C,SAAS,GAAG,CAAC,eAAe;aACnF,CAAA;QACF,CAAC;QAED,2CAA2C;QAC3C,gDAAgD;QAChD,IAAI,GAAG,IAAI,aAAa,EAAE,CAAC;YAC1B,OAAO;gBACN,WAAW,EAAE,IAAI;gBACjB,SAAS,EAAE,MAAM;gBACjB,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,aAAa,GAAG,GAAG,CAAC,GAAG,aAAa,EAAE,CAAC,CAAC;gBAC9D,MAAM,EAAE,OAAO,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,oCAAoC,aAAa,GAAG;aACjF,CAAA;QACF,CAAC;aAAM,IAAI,GAAG,IAAI,eAAe,EAAE,CAAC;YACnC,OAAO;gBACN,WAAW,EAAE,IAAI;gBACjB,SAAS,EAAE,KAAK;gBAChB,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,eAAe,CAAC,GAAG,CAAC,GAAG,GAAG,eAAe,CAAC,EAAE,CAAC,CAAC;gBAC1E,MAAM,EAAE,OAAO,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,sCAAsC,eAAe,GAAG;aACrF,CAAA;QACF,CAAC;QAED,OAAO;YACN,WAAW,EAAE,KAAK;YAClB,MAAM,EAAE,OAAO,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,qBAAqB,aAAa,IAAI,eAAe,GAAG;SACrF,CAAA;IACF,CAAC;CACD;AAjDD,kCAiDC"}