"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.StrategyFactory = exports.CustomStrategy = exports.BollingerBandsStrategy = exports.MACDStrategy = exports.RSIStrategy = exports.MovingAverageStrategy = exports.ThresholdStrategy = exports.TradingStrategy = void 0;
var TradingStrategy_1 = require("./TradingStrategy");
Object.defineProperty(exports, "TradingStrategy", { enumerable: true, get: function () { return TradingStrategy_1.TradingStrategy; } });
var ThresholdStrategy_1 = require("./ThresholdStrategy");
Object.defineProperty(exports, "ThresholdStrategy", { enumerable: true, get: function () { return ThresholdStrategy_1.ThresholdStrategy; } });
var MovingAverageStrategy_1 = require("./MovingAverageStrategy");
Object.defineProperty(exports, "MovingAverageStrategy", { enumerable: true, get: function () { return MovingAverageStrategy_1.MovingAverageStrategy; } });
var RSIStrategy_1 = require("./RSIStrategy");
Object.defineProperty(exports, "RSIStrategy", { enumerable: true, get: function () { return RSIStrategy_1.RSIStrategy; } });
var MACDStrategy_1 = require("./MACDStrategy");
Object.defineProperty(exports, "MACDStrategy", { enumerable: true, get: function () { return MACDStrategy_1.MACDStrategy; } });
var BollingerBandsStrategy_1 = require("./BollingerBandsStrategy");
Object.defineProperty(exports, "BollingerBandsStrategy", { enumerable: true, get: function () { return BollingerBandsStrategy_1.BollingerBandsStrategy; } });
var CustomStrategy_1 = require("./CustomStrategy");
Object.defineProperty(exports, "CustomStrategy", { enumerable: true, get: function () { return CustomStrategy_1.CustomStrategy; } });
var StrategyFactory_1 = require("./StrategyFactory");
Object.defineProperty(exports, "StrategyFactory", { enumerable: true, get: function () { return StrategyFactory_1.StrategyFactory; } });
//# sourceMappingURL=index.js.map