{"version": 3, "file": "CustomStrategy.js", "sourceRoot": "", "sources": ["../../../../../src/strategies/CustomStrategy.ts"], "names": [], "mappings": ";;;AACA,uDAAoE;AAEpE,MAAa,cAAe,SAAQ,iCAAe;IAClD,OAAO;QACN,OAAO,iBAAiB,CAAA;IACzB,CAAC;IAED,cAAc;QACb,OAAO,yDAAyD,CAAA;IACjE,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,SAAoB;QAClC,oCAAoC;QACpC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAA;QAE5B,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAA;QAEzC,IAAI,CAAC,UAAU,IAAI,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;YAC7C,OAAO;gBACN,WAAW,EAAE,KAAK;gBAClB,MAAM,EAAE,kCAAkC;aAC1C,CAAA;QACF,CAAC;QAED,IAAI,CAAC;YACJ,4DAA4D;YAC5D,6DAA6D;YAE7D,kCAAkC;YAClC,MAAM,OAAO,GAAG;gBACf,SAAS;gBACT,YAAY,EAAE,IAAI,CAAC,YAAY;gBAC/B,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,mBAAmB;gBACnB,sBAAsB,EAAE,CAAC,MAAc,EAAE,EAAE,CAAC,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC;gBAC/E,YAAY,EAAE,CAAC,MAAc,EAAE,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;gBAC3D,oBAAoB,EAAE,CAAC,MAAgB,EAAE,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC;gBAC7E,IAAI;gBACJ,OAAO,EAAE;oBACR,GAAG,EAAE,CAAC,GAAG,IAAW,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,GAAG,IAAI,CAAC;iBACjE;aACD,CAAA;YAED,+CAA+C;YAC/C,yEAAyE;YACzE,kDAAkD;YAElD,yCAAyC;YACzC,OAAO;gBACN,WAAW,EAAE,KAAK;gBAClB,MAAM,EAAE,6DAA6D;aACrE,CAAA;QAEF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAChB,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAA;YACxD,OAAO;gBACN,WAAW,EAAE,KAAK;gBAClB,MAAM,EAAE,0BAA0B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE;aAC5F,CAAA;QACF,CAAC;IACF,CAAC;IAED,kDAAkD;IAClD,kBAAkB,CAAC,IAAY;QAC9B,MAAM,MAAM,GAAa,EAAE,CAAA;QAE3B,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;YACjC,MAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAA;QAC3C,CAAC;QAED,kCAAkC;QAClC,kCAAkC;QAClC,oBAAoB;QACpB,qCAAqC;QAErC,OAAO;YACN,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5B,MAAM;SACN,CAAA;IACF,CAAC;IAED,4CAA4C;IAC5C,eAAe;QACd,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;mBA2BU,CAAA;IAClB,CAAC;CACD;AA9GD,wCA8GC"}