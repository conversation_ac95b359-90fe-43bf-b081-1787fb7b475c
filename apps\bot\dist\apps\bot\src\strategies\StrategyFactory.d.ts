import { StrategyType, StrategyConfig } from '../../../../shared/types';
import { TradingStrategy } from './TradingStrategy';
export declare class StrategyFactory {
    static createStrategy(type: StrategyType, config: StrategyConfig): TradingStrategy;
    static getAvailableStrategies(): Array<{
        type: StrategyType;
        name: string;
        description: string;
    }>;
    static getDefaultConfig(type: StrategyType): StrategyConfig;
}
//# sourceMappingURL=StrategyFactory.d.ts.map