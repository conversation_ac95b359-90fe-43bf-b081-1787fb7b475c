"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomStrategy = void 0;
const TradingStrategy_1 = require("./TradingStrategy");
class CustomStrategy extends TradingStrategy_1.TradingStrategy {
    getName() {
        return 'Custom Strategy';
    }
    getDescription() {
        return 'User-defined custom trading strategy with editable code';
    }
    async evaluate(priceData) {
        // Add current price data to history
        this.addPriceData(priceData);
        const customCode = this.config.customCode;
        if (!customCode || customCode.trim() === '') {
            return {
                shouldTrade: false,
                reason: 'No custom strategy code provided'
            };
        }
        try {
            // TODO: Implement safe code execution for custom strategies
            // For now, this is a placeholder that returns a safe default
            // Create a safe execution context
            const context = {
                priceData,
                priceHistory: this.priceHistory,
                config: this.config,
                // Helper functions
                getSimpleMovingAverage: (period) => this.getSimpleMovingAverage(period),
                calculateRSI: (period) => this.calculateRSI(period),
                getStandardDeviation: (values) => this.getStandardDeviation(values),
                Math,
                console: {
                    log: (...args) => console.log('[CustomStrategy]', ...args)
                }
            };
            // WARNING: This is a simplified implementation
            // In production, you should use a proper sandboxed execution environment
            // like vm2 or similar to safely execute user code
            // For now, return a placeholder response
            return {
                shouldTrade: false,
                reason: 'Custom strategy execution not yet implemented (placeholder)'
            };
        }
        catch (error) {
            console.error('Error executing custom strategy:', error);
            return {
                shouldTrade: false,
                reason: `Custom strategy error: ${error instanceof Error ? error.message : 'Unknown error'}`
            };
        }
    }
    // Method to validate custom code before execution
    validateCustomCode(code) {
        const errors = [];
        if (!code || code.trim() === '') {
            errors.push('Custom code cannot be empty');
        }
        // TODO: Add more validation rules
        // - Check for dangerous functions
        // - Validate syntax
        // - Check for required return format
        return {
            isValid: errors.length === 0,
            errors
        };
    }
    // Get template code for users to start with
    getTemplateCode() {
        return `// Custom Trading Strategy
// This function should return a TradingDecision object
// Available variables: priceData, priceHistory, config

function evaluate() {
    const currentPrice = priceData.current;
    const changePercent = priceData.changePercent;
    
    // Example: Simple threshold strategy
    const threshold = config.threshold || 0.02;
    
    if (Math.abs(changePercent) >= threshold) {
        return {
            shouldTrade: true,
            direction: priceData.trend === 'up' ? 'high' : 'low',
            confidence: Math.min(Math.abs(changePercent) / threshold, 1),
            reason: \`Price change \${changePercent.toFixed(2)}% exceeds threshold\`
        };
    }
    
    return {
        shouldTrade: false,
        reason: 'No trading signal'
    };
}

// Return the evaluation result
return evaluate();`;
    }
}
exports.CustomStrategy = CustomStrategy;
//# sourceMappingURL=CustomStrategy.js.map