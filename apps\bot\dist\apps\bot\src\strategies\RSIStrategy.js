"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RSIStrategy = void 0;
const TradingStrategy_1 = require("./TradingStrategy");
class RSIStrategy extends TradingStrategy_1.TradingStrategy {
    getName() {
        return 'RSI Strategy';
    }
    getDescription() {
        return 'Trades based on Relative Strength Index (RSI) overbought/oversold levels';
    }
    async evaluate(priceData) {
        // Add current price data to history
        this.addPriceData(priceData);
        const rsiPeriod = this.config.rsiPeriod || 14;
        const oversoldLevel = this.config.oversoldLevel || 30;
        const overboughtLevel = this.config.overboughtLevel || 70;
        const rsi = this.calculateRSI(rsiPeriod);
        if (rsi === null) {
            return {
                shouldTrade: false,
                reason: `Insufficient data for RSI calculation (need ${rsiPeriod + 1} data points)`
            };
        }
        // TODO: Implement proper RSI trading logic
        // For now, this is a placeholder implementation
        if (rsi <= oversoldLevel) {
            return {
                shouldTrade: true,
                direction: 'high',
                confidence: Math.min((oversoldLevel - rsi) / oversoldLevel, 1),
                reason: `RSI ${rsi.toFixed(2)} indicates oversold condition (< ${oversoldLevel})`
            };
        }
        else if (rsi >= overboughtLevel) {
            return {
                shouldTrade: true,
                direction: 'low',
                confidence: Math.min((rsi - overboughtLevel) / (100 - overboughtLevel), 1),
                reason: `RSI ${rsi.toFixed(2)} indicates overbought condition (> ${overboughtLevel})`
            };
        }
        return {
            shouldTrade: false,
            reason: `RSI ${rsi.toFixed(2)} in neutral zone (${oversoldLevel}-${overboughtLevel})`
        };
    }
}
exports.RSIStrategy = RSIStrategy;
//# sourceMappingURL=RSIStrategy.js.map