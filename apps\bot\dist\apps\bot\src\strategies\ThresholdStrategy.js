"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ThresholdStrategy = void 0;
const TradingStrategy_1 = require("./TradingStrategy");
class ThresholdStrategy extends TradingStrategy_1.TradingStrategy {
    getName() {
        return 'Threshold Strategy';
    }
    getDescription() {
        return 'Trades when price change exceeds a specified threshold percentage';
    }
    async evaluate(priceData) {
        const threshold = this.config.threshold || 0.02;
        // Check if price change exceeds threshold
        if (Math.abs(priceData.changePercent) >= threshold) {
            const direction = priceData.trend === 'up' ? 'high' : 'low';
            const confidence = Math.min(Math.abs(priceData.changePercent) / threshold, 1);
            return {
                shouldTrade: true,
                direction,
                confidence,
                reason: `Price change ${priceData.changePercent.toFixed(2)}% exceeds threshold ${threshold}%`
            };
        }
        return {
            shouldTrade: false,
            reason: `Price change ${priceData.changePercent.toFixed(2)}% below threshold ${threshold}%`
        };
    }
}
exports.ThresholdStrategy = ThresholdStrategy;
//# sourceMappingURL=ThresholdStrategy.js.map