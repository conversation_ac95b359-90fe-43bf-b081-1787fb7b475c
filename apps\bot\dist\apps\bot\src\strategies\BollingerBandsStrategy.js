"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BollingerBandsStrategy = void 0;
const TradingStrategy_1 = require("./TradingStrategy");
class BollingerBandsStrategy extends TradingStrategy_1.TradingStrategy {
    getName() {
        return 'Bollinger Bands Strategy';
    }
    getDescription() {
        return 'Trades based on Bollinger Bands breakouts and mean reversion';
    }
    async evaluate(priceData) {
        // Add current price data to history
        this.addPriceData(priceData);
        const period = this.config.period || 20;
        const standardDeviations = this.config.standardDeviations || 2;
        if (this.priceHistory.length < period) {
            return {
                shouldTrade: false,
                reason: `Insufficient data for Bollinger Bands calculation (need ${period} data points)`
            };
        }
        // Calculate Bollinger Bands
        const middleBand = this.getSimpleMovingAverage(period);
        if (middleBand === null) {
            return {
                shouldTrade: false,
                reason: 'Unable to calculate middle band (SMA)'
            };
        }
        const prices = this.priceHistory.slice(-period).map(p => p.current);
        const stdDev = this.getStandardDeviation(prices);
        const upperBand = middleBand + (standardDeviations * stdDev);
        const lowerBand = middleBand - (standardDeviations * stdDev);
        const currentPrice = priceData.current;
        // TODO: Implement proper Bollinger Bands trading logic
        // For now, this is a placeholder implementation
        // Trade low when price breaks above upper band (expecting reversion)
        if (currentPrice > upperBand) {
            const distance = (currentPrice - upperBand) / upperBand;
            return {
                shouldTrade: true,
                direction: 'low',
                confidence: Math.min(distance * 10, 1), // Scale confidence based on distance
                reason: `Price ${currentPrice} above upper band ${upperBand.toFixed(4)} - expecting reversion`
            };
        }
        // Trade high when price breaks below lower band (expecting reversion)
        if (currentPrice < lowerBand) {
            const distance = (lowerBand - currentPrice) / lowerBand;
            return {
                shouldTrade: true,
                direction: 'high',
                confidence: Math.min(distance * 10, 1), // Scale confidence based on distance
                reason: `Price ${currentPrice} below lower band ${lowerBand.toFixed(4)} - expecting reversion`
            };
        }
        return {
            shouldTrade: false,
            reason: `Price ${currentPrice} within bands (${lowerBand.toFixed(4)} - ${upperBand.toFixed(4)})`
        };
    }
}
exports.BollingerBandsStrategy = BollingerBandsStrategy;
//# sourceMappingURL=BollingerBandsStrategy.js.map