"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MovingAverageStrategy = void 0;
const TradingStrategy_1 = require("./TradingStrategy");
class MovingAverageStrategy extends TradingStrategy_1.TradingStrategy {
    getName() {
        return 'Moving Average Crossover';
    }
    getDescription() {
        return 'Trades based on short-term and long-term moving average crossovers';
    }
    async evaluate(priceData) {
        // Add current price data to history
        this.addPriceData(priceData);
        const shortPeriod = this.config.shortPeriod || 5;
        const longPeriod = this.config.longPeriod || 20;
        const shortMA = this.getSimpleMovingAverage(shortPeriod);
        const longMA = this.getSimpleMovingAverage(longPeriod);
        if (shortMA === null || longMA === null) {
            return {
                shouldTrade: false,
                reason: `Insufficient data for moving averages (need ${longPeriod} data points)`
            };
        }
        // TODO: Implement moving average crossover logic
        // For now, this is a placeholder implementation
        const currentPrice = priceData.current;
        // Simple logic: trade high if price is above both MAs, low if below both
        if (currentPrice > shortMA && currentPrice > longMA && shortMA > longMA) {
            return {
                shouldTrade: true,
                direction: 'high',
                confidence: 0.7,
                reason: `Price ${currentPrice} above both MAs (Short: ${shortMA.toFixed(4)}, Long: ${longMA.toFixed(4)})`
            };
        }
        else if (currentPrice < shortMA && currentPrice < longMA && shortMA < longMA) {
            return {
                shouldTrade: true,
                direction: 'low',
                confidence: 0.7,
                reason: `Price ${currentPrice} below both MAs (Short: ${shortMA.toFixed(4)}, Long: ${longMA.toFixed(4)})`
            };
        }
        return {
            shouldTrade: false,
            reason: `No clear trend signal from moving averages`
        };
    }
}
exports.MovingAverageStrategy = MovingAverageStrategy;
//# sourceMappingURL=MovingAverageStrategy.js.map