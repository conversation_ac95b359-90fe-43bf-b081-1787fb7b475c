"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.StrategyFactory = void 0;
const ThresholdStrategy_1 = require("./ThresholdStrategy");
const MovingAverageStrategy_1 = require("./MovingAverageStrategy");
const RSIStrategy_1 = require("./RSIStrategy");
const MACDStrategy_1 = require("./MACDStrategy");
const BollingerBandsStrategy_1 = require("./BollingerBandsStrategy");
const CustomStrategy_1 = require("./CustomStrategy");
class StrategyFactory {
    static createStrategy(type, config) {
        switch (type) {
            case 'threshold':
                return new ThresholdStrategy_1.ThresholdStrategy(config);
            case 'moving-average':
                return new MovingAverageStrategy_1.MovingAverageStrategy(config);
            case 'rsi':
                return new RSIStrategy_1.RSIStrategy(config);
            case 'macd':
                return new MACDStrategy_1.MACDStrategy(config);
            case 'bollinger-bands':
                return new BollingerBandsStrategy_1.BollingerBandsStrategy(config);
            case 'custom':
                return new CustomStrategy_1.CustomStrategy(config);
            default:
                throw new Error(`Unknown strategy type: ${type}`);
        }
    }
    static getAvailableStrategies() {
        return [
            {
                type: 'threshold',
                name: 'Threshold Strategy',
                description: 'Trades when price change exceeds a specified threshold percentage'
            },
            {
                type: 'moving-average',
                name: 'Moving Average Crossover',
                description: 'Trades based on short-term and long-term moving average crossovers'
            },
            {
                type: 'rsi',
                name: 'RSI Strategy',
                description: 'Trades based on Relative Strength Index (RSI) overbought/oversold levels'
            },
            {
                type: 'macd',
                name: 'MACD Strategy',
                description: 'Trades based on MACD (Moving Average Convergence Divergence) signals'
            },
            {
                type: 'bollinger-bands',
                name: 'Bollinger Bands Strategy',
                description: 'Trades based on Bollinger Bands breakouts and mean reversion'
            },
            {
                type: 'custom',
                name: 'Custom Strategy',
                description: 'User-defined custom trading strategy with editable code'
            }
        ];
    }
    static getDefaultConfig(type) {
        switch (type) {
            case 'threshold':
                return { threshold: 0.02 };
            case 'moving-average':
                return { shortPeriod: 5, longPeriod: 20 };
            case 'rsi':
                return { rsiPeriod: 14, oversoldLevel: 30, overboughtLevel: 70 };
            case 'macd':
                return { fastPeriod: 12, slowPeriod: 26, signalPeriod: 9 };
            case 'bollinger-bands':
                return { period: 20, standardDeviations: 2 };
            case 'custom':
                return { customCode: '' };
            default:
                return {};
        }
    }
}
exports.StrategyFactory = StrategyFactory;
//# sourceMappingURL=StrategyFactory.js.map