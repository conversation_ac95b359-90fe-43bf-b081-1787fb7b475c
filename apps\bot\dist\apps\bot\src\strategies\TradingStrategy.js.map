{"version": 3, "file": "TradingStrategy.js", "sourceRoot": "", "sources": ["../../../../../src/strategies/TradingStrategy.ts"], "names": [], "mappings": ";;;AASA,MAAsB,eAAe;IAIpC,YAAY,MAAsB;QAFxB,iBAAY,GAAgB,EAAE,CAAA;QAGvC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;IACrB,CAAC;IAMD,gCAAgC;IAChC,YAAY,CAAC,MAAsB;QAClC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;IACrB,CAAC;IAED,mDAAmD;IACnD,YAAY,CAAC,SAAoB;QAChC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;QAEjC,0DAA0D;QAC1D,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;YACpC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAA;QAClD,CAAC;IACF,CAAC;IAED,6CAA6C;IACnC,sBAAsB,CAAC,MAAc;QAC9C,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,MAAM,EAAE,CAAC;YACvC,OAAO,IAAI,CAAA;QACZ,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAA;QACnE,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC,CAAC,CAAA;QACzD,OAAO,GAAG,GAAG,MAAM,CAAA;IACpB,CAAC;IAED,iCAAiC;IACvB,YAAY,CAAC,SAAiB,EAAE;QACzC,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3C,OAAO,IAAI,CAAA;QACZ,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAA;QACzE,IAAI,KAAK,GAAG,CAAC,CAAA;QACb,IAAI,MAAM,GAAG,CAAC,CAAA;QAEd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACxC,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA;YACxC,IAAI,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChB,KAAK,IAAI,MAAM,CAAA;YAChB,CAAC;iBAAM,CAAC;gBACP,MAAM,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;YAC3B,CAAC;QACF,CAAC;QAED,MAAM,OAAO,GAAG,KAAK,GAAG,MAAM,CAAA;QAC9B,MAAM,OAAO,GAAG,MAAM,GAAG,MAAM,CAAA;QAE/B,IAAI,OAAO,KAAK,CAAC;YAAE,OAAO,GAAG,CAAA;QAE7B,MAAM,EAAE,GAAG,OAAO,GAAG,OAAO,CAAA;QAC5B,OAAO,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAA;IAC9B,CAAC;IAED,gDAAgD;IACtC,oBAAoB,CAAC,MAAgB;QAC9C,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAA;QACtE,MAAM,YAAY,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,CAAA;QAC/D,MAAM,cAAc,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAA;QACtF,OAAO,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;IACjC,CAAC;CACD;AAzED,0CAyEC"}