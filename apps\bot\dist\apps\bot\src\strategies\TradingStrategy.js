"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TradingStrategy = void 0;
class TradingStrategy {
    constructor(config) {
        this.priceHistory = [];
        this.config = config;
    }
    // Update strategy configuration
    updateConfig(config) {
        this.config = config;
    }
    // Add price data to history for technical analysis
    addPriceData(priceData) {
        this.priceHistory.push(priceData);
        // Keep only last 200 data points to prevent memory issues
        if (this.priceHistory.length > 200) {
            this.priceHistory = this.priceHistory.slice(-200);
        }
    }
    // Helper method to get simple moving average
    getSimpleMovingAverage(period) {
        if (this.priceHistory.length < period) {
            return null;
        }
        const prices = this.priceHistory.slice(-period).map(p => p.current);
        const sum = prices.reduce((acc, price) => acc + price, 0);
        return sum / period;
    }
    // Helper method to calculate RSI
    calculateRSI(period = 14) {
        if (this.priceHistory.length < period + 1) {
            return null;
        }
        const prices = this.priceHistory.slice(-(period + 1)).map(p => p.current);
        let gains = 0;
        let losses = 0;
        for (let i = 1; i < prices.length; i++) {
            const change = prices[i] - prices[i - 1];
            if (change > 0) {
                gains += change;
            }
            else {
                losses += Math.abs(change);
            }
        }
        const avgGain = gains / period;
        const avgLoss = losses / period;
        if (avgLoss === 0)
            return 100;
        const rs = avgGain / avgLoss;
        return 100 - (100 / (1 + rs));
    }
    // Helper method to calculate standard deviation
    getStandardDeviation(values) {
        const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
        const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
        const avgSquaredDiff = squaredDiffs.reduce((sum, val) => sum + val, 0) / values.length;
        return Math.sqrt(avgSquaredDiff);
    }
}
exports.TradingStrategy = TradingStrategy;
//# sourceMappingURL=TradingStrategy.js.map