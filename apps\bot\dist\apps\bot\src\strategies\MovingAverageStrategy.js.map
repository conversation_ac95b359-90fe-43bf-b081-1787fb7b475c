{"version": 3, "file": "MovingAverageStrategy.js", "sourceRoot": "", "sources": ["../../../../../src/strategies/MovingAverageStrategy.ts"], "names": [], "mappings": ";;;AACA,uDAAoE;AAEpE,MAAa,qBAAsB,SAAQ,iCAAe;IACzD,OAAO;QACN,OAAO,0BAA0B,CAAA;IAClC,CAAC;IAED,cAAc;QACb,OAAO,oEAAoE,CAAA;IAC5E,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,SAAoB;QAClC,oCAAoC;QACpC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAA;QAE5B,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,CAAC,CAAA;QAChD,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,EAAE,CAAA;QAE/C,MAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,WAAW,CAAC,CAAA;QACxD,MAAM,MAAM,GAAG,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAA;QAEtD,IAAI,OAAO,KAAK,IAAI,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;YACzC,OAAO;gBACN,WAAW,EAAE,KAAK;gBAClB,MAAM,EAAE,+CAA+C,UAAU,eAAe;aAChF,CAAA;QACF,CAAC;QAED,iDAAiD;QACjD,gDAAgD;QAChD,MAAM,YAAY,GAAG,SAAS,CAAC,OAAO,CAAA;QAEtC,yEAAyE;QACzE,IAAI,YAAY,GAAG,OAAO,IAAI,YAAY,GAAG,MAAM,IAAI,OAAO,GAAG,MAAM,EAAE,CAAC;YACzE,OAAO;gBACN,WAAW,EAAE,IAAI;gBACjB,SAAS,EAAE,MAAM;gBACjB,UAAU,EAAE,GAAG;gBACf,MAAM,EAAE,SAAS,YAAY,2BAA2B,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,WAAW,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG;aACzG,CAAA;QACF,CAAC;aAAM,IAAI,YAAY,GAAG,OAAO,IAAI,YAAY,GAAG,MAAM,IAAI,OAAO,GAAG,MAAM,EAAE,CAAC;YAChF,OAAO;gBACN,WAAW,EAAE,IAAI;gBACjB,SAAS,EAAE,KAAK;gBAChB,UAAU,EAAE,GAAG;gBACf,MAAM,EAAE,SAAS,YAAY,2BAA2B,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,WAAW,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG;aACzG,CAAA;QACF,CAAC;QAED,OAAO;YACN,WAAW,EAAE,KAAK;YAClB,MAAM,EAAE,4CAA4C;SACpD,CAAA;IACF,CAAC;CACD;AApDD,sDAoDC"}