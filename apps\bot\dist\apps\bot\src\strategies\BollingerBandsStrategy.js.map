{"version": 3, "file": "BollingerBandsStrategy.js", "sourceRoot": "", "sources": ["../../../../../src/strategies/BollingerBandsStrategy.ts"], "names": [], "mappings": ";;;AACA,uDAAoE;AAEpE,MAAa,sBAAuB,SAAQ,iCAAe;IAC1D,OAAO;QACN,OAAO,0BAA0B,CAAA;IAClC,CAAC;IAED,cAAc;QACb,OAAO,8DAA8D,CAAA;IACtE,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,SAAoB;QAClC,oCAAoC;QACpC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAA;QAE5B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,EAAE,CAAA;QACvC,MAAM,kBAAkB,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB,IAAI,CAAC,CAAA;QAE9D,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,MAAM,EAAE,CAAC;YACvC,OAAO;gBACN,WAAW,EAAE,KAAK;gBAClB,MAAM,EAAE,2DAA2D,MAAM,eAAe;aACxF,CAAA;QACF,CAAC;QAED,4BAA4B;QAC5B,MAAM,UAAU,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAA;QACtD,IAAI,UAAU,KAAK,IAAI,EAAE,CAAC;YACzB,OAAO;gBACN,WAAW,EAAE,KAAK;gBAClB,MAAM,EAAE,uCAAuC;aAC/C,CAAA;QACF,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAA;QACnE,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAA;QAEhD,MAAM,SAAS,GAAG,UAAU,GAAG,CAAC,kBAAkB,GAAG,MAAM,CAAC,CAAA;QAC5D,MAAM,SAAS,GAAG,UAAU,GAAG,CAAC,kBAAkB,GAAG,MAAM,CAAC,CAAA;QAE5D,MAAM,YAAY,GAAG,SAAS,CAAC,OAAO,CAAA;QAEtC,uDAAuD;QACvD,gDAAgD;QAEhD,qEAAqE;QACrE,IAAI,YAAY,GAAG,SAAS,EAAE,CAAC;YAC9B,MAAM,QAAQ,GAAG,CAAC,YAAY,GAAG,SAAS,CAAC,GAAG,SAAS,CAAA;YACvD,OAAO;gBACN,WAAW,EAAE,IAAI;gBACjB,SAAS,EAAE,KAAK;gBAChB,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,EAAE,EAAE,CAAC,CAAC,EAAE,qCAAqC;gBAC7E,MAAM,EAAE,SAAS,YAAY,qBAAqB,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,wBAAwB;aAC9F,CAAA;QACF,CAAC;QAED,sEAAsE;QACtE,IAAI,YAAY,GAAG,SAAS,EAAE,CAAC;YAC9B,MAAM,QAAQ,GAAG,CAAC,SAAS,GAAG,YAAY,CAAC,GAAG,SAAS,CAAA;YACvD,OAAO;gBACN,WAAW,EAAE,IAAI;gBACjB,SAAS,EAAE,MAAM;gBACjB,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,EAAE,EAAE,CAAC,CAAC,EAAE,qCAAqC;gBAC7E,MAAM,EAAE,SAAS,YAAY,qBAAqB,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,wBAAwB;aAC9F,CAAA;QACF,CAAC;QAED,OAAO;YACN,WAAW,EAAE,KAAK;YAClB,MAAM,EAAE,SAAS,YAAY,kBAAkB,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG;SAChG,CAAA;IACF,CAAC;CACD;AAtED,wDAsEC"}